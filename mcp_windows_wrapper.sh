#!/bin/bash

# WSL to Windows MCP Feedback Collector Wrapper
# This script calls the Windows version of mcp-feedback-collector from WSL

# Set environment variables
export PYTHONIOENCODING="utf-8"
export MCP_DIALOG_TIMEOUT="600"

# Convert WSL path to Windows path if needed
# WSL_PROJECT_PATH="/home/<USER>/works/mcp-feedback-collector"
# WIN_PROJECT_PATH=$(wslpath -w "$WSL_PROJECT_PATH")

# Call Windows Python with mcp-feedback-collector
# Option 1: Use Windows Python directly
python.exe -m mcp_feedback_collector.server "$@"

# Option 2: Use Windows mcp-feedback-collector command (if installed)
# /mnt/c/Python312/Scripts/mcp-feedback-collector.exe "$@"

# Option 3: Use cmd.exe to call Windows command
# cmd.exe /c "mcp-feedback-collector $*"
