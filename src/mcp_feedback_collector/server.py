"""
Interactive Feedback Collector MCP Server
AI reports work content, users can provide text feedback and/or image feedback
"""

import io
import base64
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import threading
import queue
from pathlib import Path
from datetime import datetime
import os

from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.utilities.types import Image as MCPImage

# Create MCP server
mcp = FastMCP(
    "Interactive Feedback Collector",
    dependencies=["pillow", "tkinter"]
)

# Configure timeout (seconds)
DEFAULT_DIALOG_TIMEOUT = 300  # 5 minutes
DIALOG_TIMEOUT = int(os.getenv("MCP_DIALOG_TIMEOUT", DEFAULT_DIALOG_TIMEOUT))

class FeedbackDialog:
    def __init__(self, work_summary: str = "", timeout_seconds: int = DIALOG_TIMEOUT):
        self.result_queue = queue.Queue()
        self.root = None
        self.work_summary = work_summary
        self.timeout_seconds = timeout_seconds
        self.selected_images = []  # Support multiple images
        self.image_preview_frame = None
        self.text_widget = None

    def show_dialog(self):
        """Display feedback collection dialog in new thread"""
        import sys
        import traceback

        def run_dialog():
            try:
                print(f"[DEBUG] Starting to create tkinter window...", file=sys.stderr)
                print(f"[DEBUG] DISPLAY environment variable: {os.getenv('DISPLAY', 'Not set')}", file=sys.stderr)

                # Check if tkinter is available
                try:
                    import tkinter as tk_test
                    root_test = tk_test.Tk()
                    root_test.withdraw()  # Hide test window
                    root_test.destroy()
                    print(f"[DEBUG] tkinter test successful", file=sys.stderr)
                except Exception as e:
                    print(f"[ERROR] tkinter test failed: {e}", file=sys.stderr)
                    self.result_queue.put({
                        'success': False,
                        'message': f'GUI environment unavailable: {str(e)}'
                    })
                    return

                self.root = tk.Tk()
                print(f"[DEBUG] tkinter.Tk() created successfully", file=sys.stderr)

                self.root.title("AI Work Report & Feedback Collection")
                self.root.geometry("700x800")
                self.root.resizable(True, True)
                self.root.configure(bg="#f5f5f5")

                # Set window icon and style
                try:
                    self.root.iconbitmap(default="")
                except Exception as e:
                    print(f"[DEBUG] Icon setting failed (normal): {e}", file=sys.stderr)
                    pass

                # Center window
                try:
                    self.root.eval('tk::PlaceWindow . center')
                    print(f"[DEBUG] Window centered successfully", file=sys.stderr)
                except Exception as e:
                    print(f"[DEBUG] Window centering failed, using default position: {e}", file=sys.stderr)
                    # Manual centering
                    self.root.update_idletasks()
                    width = self.root.winfo_width()
                    height = self.root.winfo_height()
                    x = (self.root.winfo_screenwidth() // 2) - (width // 2)
                    y = (self.root.winfo_screenheight() // 2) - (height // 2)
                    self.root.geometry(f'{width}x{height}+{x}+{y}')

                # Force window to display on top and maintain focus
                self.root.lift()
                self.root.attributes('-topmost', True)
                self.root.focus_force()

                # Window flashing to get user attention
                def flash_window():
                    for i in range(3):
                        self.root.attributes('-topmost', False)
                        self.root.update()
                        self.root.after(200)
                        self.root.attributes('-topmost', True)
                        self.root.update()
                        self.root.after(200)
                    # Keep on top for a while
                    self.root.after(5000, lambda: self.root.attributes('-topmost', False))

                self.root.after(100, flash_window)

                print(f"[DEBUG] Starting to create UI components...", file=sys.stderr)
                # Create interface
                self.create_widgets()
                print(f"[DEBUG] UI components creation completed", file=sys.stderr)

                # Ensure window is visible
                self.root.deiconify()
                self.root.focus_force()

                print(f"[DEBUG] Starting mainloop...", file=sys.stderr)
                # Run main loop
                self.root.mainloop()
                print(f"[DEBUG] mainloop ended", file=sys.stderr)

            except Exception as e:
                error_msg = f"GUI creation failed: {str(e)}\n{traceback.format_exc()}"
                print(f"[ERROR] {error_msg}", file=sys.stderr)
                self.result_queue.put({
                    'success': False,
                    'message': error_msg
                })

        # Run dialog in new thread
        print(f"[DEBUG] Starting GUI thread...", file=sys.stderr)
        dialog_thread = threading.Thread(target=run_dialog)
        dialog_thread.daemon = True
        dialog_thread.start()

        # Wait for result
        print(f"[DEBUG] Waiting for user action, timeout: {self.timeout_seconds} seconds", file=sys.stderr)
        try:
            result = self.result_queue.get(timeout=self.timeout_seconds)
            print(f"[DEBUG] Received result: {result.get('success', False)}", file=sys.stderr)
            return result
        except queue.Empty:
            print(f"[DEBUG] Operation timeout", file=sys.stderr)
            return None

    def create_widgets(self):
        """Create beautiful UI components"""
        # Main frame
        main_frame = tk.Frame(self.root, bg="#f5f5f5")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Title
        title_label = tk.Label(
            main_frame,
            text="AI Work Report & Feedback Collection",
            font=("Arial", 16, "bold"),
            bg="#f5f5f5",
            fg="#2c3e50"
        )
        title_label.pack(pady=(0, 20))

        # 1. Work Report Area
        report_frame = tk.LabelFrame(
            main_frame,
            text="AI Work Report",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#34495e",
            relief=tk.RAISED,
            bd=2
        )
        report_frame.pack(fill=tk.X, pady=(0, 15))

        report_text = tk.Text(
            report_frame,
            height=5,
            wrap=tk.WORD,
            bg="#ecf0f1",
            fg="#2c3e50",
            font=("Arial", 10),
            relief=tk.FLAT,
            bd=5,
            state=tk.DISABLED
        )
        report_text.pack(fill=tk.X, padx=15, pady=15)

        # Display work report content
        report_text.config(state=tk.NORMAL)
        report_text.insert(tk.END, self.work_summary or "Work completed in this session...")
        report_text.config(state=tk.DISABLED)

        # 2. User Feedback Text Area
        feedback_frame = tk.LabelFrame(
            main_frame,
            text="Your Text Feedback (Optional)",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#34495e",
            relief=tk.RAISED,
            bd=2
        )
        feedback_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Text input box
        self.text_widget = scrolledtext.ScrolledText(
            feedback_frame,
            height=6,
            wrap=tk.WORD,
            font=("Arial", 10),
            bg="#ffffff",
            fg="#2c3e50",
            relief=tk.FLAT,
            bd=5,
            insertbackground="#3498db"
        )
        self.text_widget.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        self.text_widget.insert(tk.END, "Please enter your feedback, suggestions, or questions here...")
        self.text_widget.bind("<FocusIn>", self.clear_placeholder)

        # 3. Image Selection Area
        image_frame = tk.LabelFrame(
            main_frame,
            text="Image Feedback (Optional, Multiple Images Supported)",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#34495e",
            relief=tk.RAISED,
            bd=2
        )
        image_frame.pack(fill=tk.X, pady=(0, 15))

        # Image operation buttons
        btn_frame = tk.Frame(image_frame, bg="#ffffff")
        btn_frame.pack(fill=tk.X, padx=15, pady=10)

        # Button style
        btn_style = {
            "font": ("Arial", 10, "bold"),
            "relief": tk.FLAT,
            "bd": 0,
            "cursor": "hand2",
            "height": 2
        }

        tk.Button(
            btn_frame,
            text="Select Image Files",
            command=self.select_image_file,
            bg="#3498db",
            fg="white",
            width=18,
            **btn_style
        ).pack(side=tk.LEFT, padx=(0, 8))

        tk.Button(
            btn_frame,
            text="Paste from Clipboard",
            command=self.paste_from_clipboard,
            bg="#2ecc71",
            fg="white",
            width=18,
            **btn_style
        ).pack(side=tk.LEFT, padx=4)

        tk.Button(
            btn_frame,
            text="Clear All Images",
            command=self.clear_all_images,
            bg="#e74c3c",
            fg="white",
            width=18,
            **btn_style
        ).pack(side=tk.LEFT, padx=8)

        # Image preview area (scrollable)
        preview_container = tk.Frame(image_frame, bg="#ffffff")
        preview_container.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Create scrollable canvas
        canvas = tk.Canvas(preview_container, height=120, bg="#f8f9fa", relief=tk.SUNKEN, bd=1)
        scrollbar = tk.Scrollbar(preview_container, orient="horizontal", command=canvas.xview)
        self.image_preview_frame = tk.Frame(canvas, bg="#f8f9fa")

        self.image_preview_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.image_preview_frame, anchor="nw")
        canvas.configure(xscrollcommand=scrollbar.set)

        canvas.pack(side="top", fill="x")
        scrollbar.pack(side="bottom", fill="x")

        # Initial hint
        self.update_image_preview()

        # 4. Action Buttons
        button_frame = tk.Frame(main_frame, bg="#f5f5f5")
        button_frame.pack(fill=tk.X, pady=(15, 0))

        # Main action buttons
        submit_btn = tk.Button(
            button_frame,
            text="Submit Feedback",
            command=self.submit_feedback,
            font=("Arial", 12, "bold"),
            bg="#27ae60",
            fg="white",
            width=18,
            height=2,
            relief=tk.FLAT,
            bd=0,
            cursor="hand2"
        )
        submit_btn.pack(side=tk.LEFT, padx=(0, 15))

        cancel_btn = tk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel,
            font=("Arial", 12),
            bg="#95a5a6",
            fg="white",
            width=18,
            height=2,
            relief=tk.FLAT,
            bd=0,
            cursor="hand2"
        )
        cancel_btn.pack(side=tk.LEFT)

        # Info text
        info_label = tk.Label(
            main_frame,
            text="Tip: You can provide text feedback only, images only, or both (multiple images supported)",
            font=("Arial", 9),
            fg="#7f8c8d",
            bg="#f5f5f5"
        )
        info_label.pack(pady=(15, 0))

    def clear_placeholder(self, event):
        """Clear placeholder text"""
        placeholder_text = "Please enter your feedback, suggestions, or questions here..."
        if self.text_widget.get(1.0, tk.END).strip() == placeholder_text:
            self.text_widget.delete(1.0, tk.END)

    def select_image_file(self):
        """Select image files (multiple selection supported)"""
        file_types = [
            ("Image Files", "*.png *.jpg *.jpeg *.gif *.bmp *.webp"),
            ("PNG Files", "*.png"),
            ("JPEG Files", "*.jpg *.jpeg"),
            ("All Files", "*.*")
        ]

        file_paths = filedialog.askopenfilenames(
            title="Select Image Files (Multiple Selection)",
            filetypes=file_types
        )

        for file_path in file_paths:
            try:
                # Read and validate image
                with open(file_path, 'rb') as f:
                    image_data = f.read()

                img = Image.open(io.BytesIO(image_data))
                self.selected_images.append({
                    'data': image_data,
                    'source': f'File: {Path(file_path).name}',
                    'size': img.size,
                    'image': img
                })

            except Exception as e:
                messagebox.showerror("Error", f"Cannot read image file {Path(file_path).name}: {str(e)}")

        self.update_image_preview()

    def paste_from_clipboard(self):
        """Paste image from clipboard"""
        try:
            from PIL import ImageGrab
            img = ImageGrab.grabclipboard()

            if img:
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                image_data = buffer.getvalue()

                self.selected_images.append({
                    'data': image_data,
                    'source': 'Clipboard',
                    'size': img.size,
                    'image': img
                })

                self.update_image_preview()
            else:
                messagebox.showwarning("Warning", "No image data in clipboard")

        except Exception as e:
            messagebox.showerror("Error", f"Cannot get image from clipboard: {str(e)}")

    def clear_all_images(self):
        """Clear all selected images"""
        self.selected_images = []
        self.update_image_preview()

    def update_image_preview(self):
        """Update image preview display"""
        # Clear existing preview
        for widget in self.image_preview_frame.winfo_children():
            widget.destroy()

        if not self.selected_images:
            # Show no image selected message
            no_image_label = tk.Label(
                self.image_preview_frame,
                text="No images selected",
                bg="#f8f9fa",
                fg="#95a5a6",
                font=("Arial", 10)
            )
            no_image_label.pack(pady=20)
        else:
            # Display all image previews
            for i, img_info in enumerate(self.selected_images):
                try:
                    # Create individual image preview container
                    img_container = tk.Frame(self.image_preview_frame, bg="#ffffff", relief=tk.RAISED, bd=1)
                    img_container.pack(side=tk.LEFT, padx=5, pady=5)

                    # Create thumbnail
                    img_copy = img_info['image'].copy()
                    img_copy.thumbnail((100, 80), Image.Resampling.LANCZOS)

                    # Convert to tkinter compatible format
                    photo = ImageTk.PhotoImage(img_copy)

                    # Image label
                    img_label = tk.Label(img_container, image=photo, bg="#ffffff")
                    img_label.image = photo  # Keep reference
                    img_label.pack(padx=5, pady=5)

                    # Image info
                    info_text = f"{img_info['source']}\n{img_info['size'][0]}x{img_info['size'][1]}"
                    info_label = tk.Label(
                        img_container,
                        text=info_text,
                        font=("Arial", 8),
                        bg="#ffffff",
                        fg="#7f8c8d"
                    )
                    info_label.pack(pady=(0, 5))

                    # Delete button
                    del_btn = tk.Button(
                        img_container,
                        text="×",
                        command=lambda idx=i: self.remove_image(idx),
                        font=("Arial", 10, "bold"),
                        bg="#e74c3c",
                        fg="white",
                        width=3,
                        relief=tk.FLAT,
                        cursor="hand2"
                    )
                    del_btn.pack(pady=(0, 5))

                except Exception as e:
                    print(f"Preview update failed: {e}")

    def remove_image(self, index):
        """Remove image at specified index"""
        if 0 <= index < len(self.selected_images):
            self.selected_images.pop(index)
            self.update_image_preview()

    def submit_feedback(self):
        """Submit feedback"""
        # Get text content
        text_content = self.text_widget.get(1.0, tk.END).strip()
        placeholder_text = "Please enter your feedback, suggestions, or questions here..."
        if text_content == placeholder_text:
            text_content = ""

        # Check if there is content
        has_text = bool(text_content)
        has_images = bool(self.selected_images)

        if not has_text and not has_images:
            messagebox.showwarning("Warning", "Please provide at least text feedback or image feedback")
            return

        # Prepare result data
        result = {
            'success': True,
            'text_feedback': text_content if has_text else None,
            'images': [img['data'] for img in self.selected_images] if has_images else None,
            'image_sources': [img['source'] for img in self.selected_images] if has_images else None,
            'has_text': has_text,
            'has_images': has_images,
            'image_count': len(self.selected_images),
            'timestamp': datetime.now().isoformat()
        }

        self.result_queue.put(result)
        self.root.destroy()

    def cancel(self):
        """Cancel operation"""
        self.result_queue.put({
            'success': False,
            'message': 'User cancelled feedback submission'
        })
        self.root.destroy()


@mcp.tool()
def collect_feedback(work_summary: str = "", timeout_seconds: int = DIALOG_TIMEOUT) -> list:
    """
    Interactive tool for collecting user feedback. AI can report completed work, users can provide text and/or image feedback.

    Args:
        work_summary: AI's work completion report
        timeout_seconds: Dialog timeout (seconds), default 300 seconds (5 minutes)

    Returns:
        List containing user feedback content, may include text and images
    """
    import sys

    print(f"[DEBUG] collect_feedback called", file=sys.stderr)
    print(f"[DEBUG] work_summary: {work_summary[:100]}..." if len(work_summary) > 100 else f"[DEBUG] work_summary: {work_summary}", file=sys.stderr)
    print(f"[DEBUG] timeout_seconds: {timeout_seconds}", file=sys.stderr)

    try:
        dialog = FeedbackDialog(work_summary, timeout_seconds)
        print(f"[DEBUG] FeedbackDialog created successfully", file=sys.stderr)

        result = dialog.show_dialog()
        print(f"[DEBUG] show_dialog returned: {result}", file=sys.stderr)

        if result is None:
            error_msg = f"Operation timeout ({timeout_seconds} seconds), please retry"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            raise Exception(error_msg)

        if not result['success']:
            error_msg = result.get('message', 'User cancelled feedback submission')
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            raise Exception(error_msg)

        # Build return content list
        feedback_items = []

        # Add text feedback
        if result['has_text']:
            from mcp.types import TextContent
            feedback_items.append(TextContent(
                type="text",
                text=f"User text feedback: {result['text_feedback']}\nSubmission time: {result['timestamp']}"
            ))

        # Add image feedback
        if result['has_images']:
            for image_data, source in zip(result['images'], result['image_sources']):
                feedback_items.append(MCPImage(data=image_data, format='png'))

        print(f"[DEBUG] Returning {len(feedback_items)} feedback items", file=sys.stderr)
        return feedback_items

    except Exception as e:
        print(f"[ERROR] collect_feedback exception: {str(e)}", file=sys.stderr)
        import traceback
        print(f"[ERROR] Stack trace: {traceback.format_exc()}", file=sys.stderr)
        raise


@mcp.tool()
def pick_image() -> MCPImage:
    """
    Pop up image selection dialog, let user select image file or paste from clipboard.
    User can select local image file, or take screenshot to clipboard then paste.
    """
    # Use simplified dialog for image selection only
    dialog = FeedbackDialog()
    dialog.work_summary = "Please select an image"

    # Create simplified image selection dialog
    def simple_image_dialog():
        root = tk.Tk()
        root.title("Select Image")
        root.geometry("400x300")
        root.resizable(False, False)
        root.eval('tk::PlaceWindow . center')

        selected_image = {'data': None}

        def select_file():
            file_path = filedialog.askopenfilename(
                title="Select Image File",
                filetypes=[("Image Files", "*.png *.jpg *.jpeg *.gif *.bmp *.webp")]
            )
            if file_path:
                try:
                    with open(file_path, 'rb') as f:
                        selected_image['data'] = f.read()
                    root.destroy()
                except Exception as e:
                    messagebox.showerror("Error", f"Cannot read image: {e}")

        def paste_clipboard():
            try:
                from PIL import ImageGrab
                img = ImageGrab.grabclipboard()
                if img:
                    buffer = io.BytesIO()
                    img.save(buffer, format='PNG')
                    selected_image['data'] = buffer.getvalue()
                    root.destroy()
                else:
                    messagebox.showwarning("Warning", "No image in clipboard")
            except Exception as e:
                messagebox.showerror("Error", f"Clipboard operation failed: {e}")

        def cancel():
            root.destroy()

        # Interface
        tk.Label(root, text="Please select image source", font=("Arial", 14, "bold")).pack(pady=20)

        btn_frame = tk.Frame(root)
        btn_frame.pack(pady=20)

        tk.Button(btn_frame, text="Select Image File", font=("Arial", 12),
                 width=20, height=2, command=select_file).pack(pady=10)
        tk.Button(btn_frame, text="Paste from Clipboard", font=("Arial", 12),
                 width=20, height=2, command=paste_clipboard).pack(pady=10)
        tk.Button(btn_frame, text="Cancel", font=("Arial", 12),
                 width=20, height=1, command=cancel).pack(pady=10)

        root.mainloop()
        return selected_image['data']

    image_data = simple_image_dialog()

    if image_data is None:
        raise Exception("No image selected or operation cancelled")

    return MCPImage(data=image_data, format='png')


@mcp.tool()
def check_gui_environment() -> str:
    """
    Check if GUI environment is available, for diagnosing dialog display issues
    """
    import sys
    import platform

    result = []
    result.append(f"Operating System: {platform.system()} {platform.release()}")
    result.append(f"Python Version: {platform.python_version()}")

    # Check DISPLAY environment variable
    display = os.getenv('DISPLAY')
    result.append(f"DISPLAY environment variable: {display if display else 'Not set'}")

    # Check if running in WSL
    try:
        with open('/proc/version', 'r') as f:
            version_info = f.read().lower()
            if 'microsoft' in version_info or 'wsl' in version_info:
                result.append("WSL environment detected")
    except:
        pass

    # Check if tkinter is available
    try:
        import tkinter as tk_test
        root_test = tk_test.Tk()
        root_test.withdraw()
        root_test.destroy()
        result.append("✅ tkinter available")
    except Exception as e:
        result.append(f"❌ tkinter unavailable: {str(e)}")

    # Check if PIL is available
    try:
        from PIL import Image, ImageTk
        result.append("✅ PIL/Pillow available")
    except Exception as e:
        result.append(f"❌ PIL/Pillow unavailable: {str(e)}")

    return "\n".join(result)


@mcp.tool()
def get_image_info(image_path: str) -> str:
    """
    Get information about image at specified path (dimensions, format, etc.)

    Args:
        image_path: Image file path
    """
    try:
        path = Path(image_path)
        if not path.exists():
            return f"File does not exist: {image_path}"

        with Image.open(path) as img:
            info = {
                "Filename": path.name,
                "Format": img.format,
                "Dimensions": f"{img.width} x {img.height}",
                "Mode": img.mode,
                "File Size": f"{path.stat().st_size / 1024:.1f} KB"
            }

        return "\n".join([f"{k}: {v}" for k, v in info.items()])

    except Exception as e:
        return f"Failed to get image info: {str(e)}"


if __name__ == "__main__":
    mcp.run()


def main():
    """Main entry point for the mcp-feedback-collector command."""
    mcp.run()